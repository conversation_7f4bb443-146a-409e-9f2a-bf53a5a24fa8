# 拖拽文件上传Windows路径处理修复

## 问题描述

在Windows系统下拖拽文件夹时出现严重的文件平铺问题：

### 具体表现
- **错误行为**：拖拽单个包含531个文件的文件夹 → 被识别为531个独立文件夹的混合结构上传
- **路径异常**：文件路径显示使用反斜杠分隔符（如 `pwa\.DS_Store`、`pwa\.git\COMMIT_EDITMSG`）
- **平台差异**：Mac系统下相同操作正常，问题特定于Windows平台
- **上传策略错误**：应该是 `folder` 类型，却被误判为 `mixed` 类型

### 浏览器日志示例
```
混合结构上传: 531个文件夹, 0个单独文件
```

## 根本原因分析

### 1. 路径分隔符差异
- **Windows系统**：使用反斜杠 `\` 作为路径分隔符
- **Unix/Mac系统**：使用正斜杠 `/` 作为路径分隔符
- **问题**：代码只处理了Unix/Mac的正斜杠格式

### 2. 主进程路径处理
在 `electron/main.ts` 中，`path.relative()` 在Windows下返回反斜杠路径：
```typescript
// Windows下返回: "pwa\\.git\\COMMIT_EDITMSG"
// Mac下返回: "pwa/.git/COMMIT_EDITMSG"
const relativePath = path.relative(baseDir, fullPath);
```

### 3. 前端路径检测逻辑缺陷
在 `src/lib/upload-utils.ts` 中，只检查正斜杠：
```typescript
// 修复前的问题代码 - 只检查 Unix 路径分隔符
const hasRealFolderStructure = files.some((file) => {
  const relativePath = (file as any).webkitRelativePath as string;
  return relativePath && relativePath.includes("/"); // ❌ 只检查正斜杠
});
```

### 4. 文件夹结构识别失败
由于无法识别Windows路径的文件夹结构，每个文件都被当作独立的"文件夹"处理。

## 修复方案

### 1. 添加跨平台路径处理工具函数

```typescript
/**
 * 跨平台路径处理工具函数
 */
function normalizePathSeparators(path: string): string {
  // 将所有反斜杠转换为正斜杠，统一使用 Unix 风格的路径分隔符
  return path.replace(/\\/g, "/");
}

function hasPathSeparator(path: string): boolean {
  // 检查路径是否包含分隔符（支持 Windows 和 Unix 风格）
  return path.includes("/") || path.includes("\\");
}

function getTopLevelFolder(relativePath: string): string {
  // 统一路径分隔符后获取顶级文件夹名称
  const normalizedPath = normalizePathSeparators(relativePath);
  const pathParts = normalizedPath.split("/");
  return pathParts[0];
}
```

### 2. 修复主进程路径统一化

在 `electron/main.ts` 中统一路径分隔符：
```typescript
// 统一路径分隔符：将 Windows 的反斜杠转换为正斜杠
// 这样前端处理时就不需要考虑平台差异
relativePath = relativePath.replace(/\\/g, "/");
```

### 3. 增强前端路径检测逻辑

在 `src/lib/upload-utils.ts` 中支持跨平台路径分隔符：
```typescript
// 检查是否有真正的文件夹结构（webkitRelativePath 包含路径分隔符）
// 支持 Windows (\) 和 Unix (/) 两种路径分隔符
const hasRealFolderStructure = files.some((file) => {
  const relativePath = (file as any).webkitRelativePath as string;
  return relativePath && hasPathSeparator(relativePath);
});
```

### 4. 优化文件夹路径分组逻辑

```typescript
files.forEach((file) => {
  const relativePath = (file as any).webkitRelativePath as string;
  if (relativePath && hasPathSeparator(relativePath)) {
    // 获取顶级文件夹名称（支持跨平台路径分隔符）
    const topLevelFolder = getTopLevelFolder(relativePath);
    // ... 分组逻辑
  }
});
```

### 5. 增强 webkitRelativePath 设置逻辑

在 `src/composables/useDragAndDrop.ts` 中：
```typescript
// 支持跨平台路径分隔符检查
const hasPathStructure = relativePath && 
  relativePath !== fileName && 
  (relativePath.includes("/") || relativePath.includes("\\"));

if (hasPathStructure) {
  Object.defineProperty(file, "webkitRelativePath", {
    value: relativePath,
    writable: false,
  });
}
```

## 修复效果验证

### 测试场景覆盖
1. **Windows单个文件拖拽**
   - 输入：`relativePath: "document.pdf"`
   - 结果：`isFromDirectory: false`, `uploadStrategy: "single"` ✅

2. **Windows文件夹拖拽**
   - 输入：`relativePath: "pwa\\index.html"`
   - 结果：`isFromDirectory: true`, `uploadStrategy: "folder"` ✅

3. **Windows多级文件夹**
   - 输入：`relativePath: "pwa\\.git\\COMMIT_EDITMSG"`
   - 结果：`isFromDirectory: true`, `uploadStrategy: "folder"` ✅

4. **批量文件夹文件**
   - 输入：531个文件，路径如 `pwa/file1.txt`, `pwa/file2.txt`...
   - 结果：`uploadStrategy: "folder"` (不再是 `mixed`) ✅

### 修复前后对比

| 场景 | 修复前 | 修复后 |
|------|--------|--------|
| Windows单文件 | 可能误判 | ✅ 正确识别为 `single` |
| Windows文件夹 | ❌ 识别为 `mixed` (531个文件夹) | ✅ 正确识别为 `folder` |
| 跨平台兼容 | ❌ 仅支持Unix/Mac | ✅ 支持Windows/Unix/Mac |
| 路径处理 | ❌ 路径分隔符不统一 | ✅ 统一使用正斜杠 |

## 相关文件修改

- `src/lib/upload-utils.ts` - 添加跨平台路径处理工具函数，修复上传策略检测
- `src/composables/useDragAndDrop.ts` - 增强路径结构检查逻辑
- `electron/main.ts` - 统一主进程路径分隔符输出

## 注意事项

1. ✅ 保持向后兼容性，Mac/Unix系统行为不变
2. ✅ 主进程统一输出正斜杠路径，简化前端处理
3. ✅ 工具函数可复用，便于后续路径处理需求
4. ✅ 修复不影响其他上传方式（文件选择对话框等）
