# Windows系统压缩功能路径处理修复

## 问题描述

在Windows系统下，文件压缩功能出现严重的路径处理错误，导致压缩任务失败。

### 错误表现
从 `main.log` 日志可以看到以下错误：
```
❌ 路径验证失败: 以下路径无效或无法访问: C:\Users\<USER>\Desktop\pwa\.DS_Store (\C:\Users\<USER>\Desktop\pwa\C:\Users\<USER>\Desktop\pwa\.DS_Store)
```

### 错误分析
错误信息显示的格式是：`relativePath (fullPath)`
- `relativePath` = `C:\Users\<USER>\Desktop\pwa\.DS_Store` （应该是相对路径，但显示为绝对路径）
- `fullPath` = `\C:\Users\<USER>\Desktop\pwa\C:\Users\<USER>\Desktop\pwa\.DS_Store` （明显的路径重复拼接错误）

## 问题根源

### 1. Windows路径构建错误
在 `electron/archive/archiveManager.ts` 的 `findTrueCommonParent` 方法中（第366行）：

```typescript
// 修复前的错误代码
const commonParent = path.sep + path.join(...commonComponents);
```

在Windows系统下：
- `path.sep` = `\`
- `commonComponents` = `['C:', 'Users', 'Administrator', 'Desktop']`
- 结果 = `\C:\Users\<USER>\Desktop` （无效的Windows路径）

### 2. 路径验证失败
由于 `commonParent` 路径格式错误，在路径验证时（第412行）：
```typescript
const fullPath = path.join(workingDir, relativePath);
```
会产生重复拼接的错误路径。

### 3. 相对路径计算错误
由于 `commonParent` 错误，`path.relative(commonParent, p)` 的结果也是错误的。

## 修复方案

### 1. 修复Windows下app.asar.unpacked路径转换

**问题**：在Windows打包环境下，7z可执行文件路径指向 `app.asar` 内部，无法访问。

**修复**：在压缩和解压缩模块中都应用跨平台的路径转换逻辑：

```typescript
// 跨平台处理 app.asar 路径转换
// 支持 Unix 风格路径 (app.asar/node_modules) 和 Windows 风格路径 (app.asar\node_modules)
const needsUnpacking = (sevenZipPath.includes("app.asar/node_modules") || sevenZipPath.includes("app.asar\\node_modules"))
                      && !sevenZipPath.includes("app.asar.unpacked");

if (needsUnpacking) {
  // 处理 Unix 风格路径分隔符
  let unpackedPath = sevenZipPath.replace("app.asar/node_modules", "app.asar.unpacked/node_modules");
  // 处理 Windows 风格路径分隔符
  unpackedPath = unpackedPath.replace("app.asar\\node_modules", "app.asar.unpacked\\node_modules");

  sevenZipPath = unpackedPath;
}
```

### 2. 修复公共父目录路径构建逻辑

**问题**：Windows路径构建错误，产生 `\C:\Users\<USER>\），Unix下返回 /
  if (process.platform === 'win32' && normalizedPaths.length > 0) {
    const firstPath = normalizedPaths[0];
    const parsed = path.parse(firstPath);
    return parsed.root; // 例如 "C:\"
  }
  return path.sep;
}
```

### 3. 添加调试日志

为了更好地诊断路径处理问题，添加了详细的调试日志：

```typescript
archiveLogger.info(`📦 标准化路径:`, normalizedPaths);
archiveLogger.info(`📦 公共父目录: ${commonParent}`);
archiveLogger.info(`📦 计算的相对路径:`, relativePaths);
```

## 修复效果

### 修复前
- Windows路径构建：`\C:\Users\<USER>\Desktop` ❌
- 相对路径计算：错误的绝对路径 ❌
- 路径验证：重复拼接导致失败 ❌

### 修复后
- Windows路径构建：`C:\Users\<USER>\Desktop` ✅
- 相对路径计算：正确的相对路径 ✅
- 路径验证：正确的路径拼接 ✅

## 跨平台兼容性

修复方案确保了跨平台兼容性：

| 平台 | 路径分隔符 | 根目录处理 | 路径构建方式 |
|------|------------|------------|--------------|
| Windows | `\` | `C:\`, `D:\` 等 | `path.join(...components)` |
| Unix/Linux/Mac | `/` | `/` | `path.sep + path.join(...components)` |

## 相关文件

- `electron/archive/archiveManager.ts` - 主要修复文件
  - `findTrueCommonParent` 方法：修复路径构建逻辑
  - `calculateRelativePaths` 方法：添加调试日志
  - `resolve7zipPath` 函数：修复Windows下app.asar.unpacked路径转换

- `electron/7z-extractor/worker.ts` - 解压缩模块修复
  - `get7zipPath` 方法：同步应用app.asar.unpacked路径转换修复

## 测试验证

修复后的逻辑应该能够：
1. ✅ 正确处理Windows绝对路径格式
2. ✅ 正确计算公共父目录
3. ✅ 正确计算相对路径
4. ✅ 通过路径验证检查
5. ✅ 成功执行7z压缩命令

## 注意事项

1. **平台检测**：使用 `process.platform === 'win32'` 来检测Windows系统
2. **路径解析**：使用 `path.parse(firstPath).root` 获取Windows根目录
3. **向后兼容**：保持Unix/Linux/Mac系统的原有行为不变
4. **调试支持**：添加详细日志便于后续问题诊断

## 后续建议

1. 在Windows环境下进行完整的压缩功能测试
2. 验证不同类型的文件和文件夹结构
3. 确认7z命令在修复后的工作目录下正常执行
4. 监控日志输出，确保路径处理符合预期
