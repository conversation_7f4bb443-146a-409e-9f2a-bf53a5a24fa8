import { EventEmitter } from "events";
import * as path from "path";
import * as fs from "fs/promises";
import * as os from "os";
import { spawn } from "child_process";
import type { ArchiveTask, ArchiveConfig, ArchiveOptions, ArchiveResult, ArchiveStatus, SmartPackingAnalysis, BatchPackingOptions } from "./types";
import { archiveLogger } from "./logger";

// 7zip-bin 路径解析函数
function resolve7zipPath(): string {
  const fs = require("fs");

  try {
    const sevenBin = require("7zip-bin");
    let sevenZipPath = sevenBin.path7za;

    // 跨平台处理 app.asar 路径转换
    // 支持 Unix 风格路径 (app.asar/node_modules) 和 Windows 风格路径 (app.asar\node_modules)
    const needsUnpacking = (sevenZipPath.includes("app.asar/node_modules") || sevenZipPath.includes("app.asar\\node_modules")) && !sevenZipPath.includes("app.asar.unpacked");

    if (needsUnpacking) {
      // 处理 Unix 风格路径分隔符
      let unpackedPath = sevenZipPath.replace("app.asar/node_modules", "app.asar.unpacked/node_modules");
      // 处理 Windows 风格路径分隔符
      unpackedPath = unpackedPath.replace("app.asar\\node_modules", "app.asar.unpacked\\node_modules");

      archiveLogger.info(`📦 转换asar路径: ${sevenZipPath} -> ${unpackedPath}`);
      sevenZipPath = unpackedPath;
    }

    // 验证7z可执行文件是否存在
    if (!fs.existsSync(sevenZipPath)) {
      throw new Error(`7z可执行文件不存在: ${sevenZipPath}`);
    }

    archiveLogger.info(`📦 7zip-bin加载成功，路径: ${sevenZipPath}`);
    return sevenZipPath;
  } catch (error) {
    const errorMsg = `7zip-bin不可用: ${error instanceof Error ? error.message : String(error)}`;
    archiveLogger.error(`❌ ${errorMsg}`);
    throw new Error(errorMsg);
  }
}

export class ArchiveManager extends EventEmitter {
  private tasks: Map<string, ArchiveTask> = new Map();
  private config: ArchiveConfig;
  private activeTasks: Set<string> = new Set();

  constructor(config?: Partial<ArchiveConfig>) {
    super();
    this.config = {
      compressionLevel: 0, // 默认仅存储，不压缩
      format: "7z",
      tempDir: os.tmpdir(),
      maxConcurrent: 2,
      ...config,
    };
  }

  /**
   * 智能分析是否应该进行批量打包
   */
  async analyzeSmartPacking(filePaths: string[], options?: BatchPackingOptions): Promise<SmartPackingAnalysis> {
    const threshold = options?.threshold || 50;

    try {
      let totalSize = 0;
      let fileCount = 0;

      // 计算总大小和文件数量
      for (const filePath of filePaths) {
        try {
          const stats = await fs.stat(filePath);
          if (stats.isFile()) {
            totalSize += stats.size;
            fileCount++;
          } else if (stats.isDirectory()) {
            const dirStats = await this.calculateDirectoryStats(filePath);
            totalSize += dirStats.size;
            fileCount += dirStats.fileCount;
          }
        } catch (error) {
          archiveLogger.warn(`无法获取文件统计信息: ${filePath}`, error);
        }
      }

      // 仅基于文件数量判断是否应该打包，移除文件大小限制
      const shouldPack = fileCount >= threshold;

      // 估算打包时间（基于文件数量和大小）
      const estimatedPackTime = Math.max(5, Math.ceil(fileCount / 100) + Math.ceil(totalSize / (50 * 1024 * 1024)));

      // 估算节省的上传时间（假设打包后减少HTTP请求开销）
      const estimatedUploadTimeSavings = Math.max(0, (fileCount - 1) * 2); // 每个文件节省2秒的HTTP开销

      return {
        shouldPack,
        reason: shouldPack ? `文件数量 ${fileCount} 超过阈值 ${threshold}，建议打包上传` : `文件数量 ${fileCount} 未达到阈值 ${threshold}`,
        fileCount,
        totalSize,
        estimatedPackTime,
        estimatedSavings: {
          uploadTime: estimatedUploadTimeSavings,
          bandwidth: Math.max(0, totalSize * 0.1), // 假设能节省10%的带宽
        },
      };
    } catch (error) {
      return {
        shouldPack: false,
        reason: `分析失败: ${error}`,
        fileCount: 0,
        totalSize: 0,
        estimatedPackTime: 0,
        estimatedSavings: { uploadTime: 0, bandwidth: 0 },
      };
    }
  }

  /**
   * 创建压缩任务
   */
  async createArchiveTask(sourcePaths: string[], options?: ArchiveOptions): Promise<string> {
    const taskId = this.generateTaskId();
    const archiveName = options?.name || `archive_${Date.now()}`;
    const outputPath = path.join(this.config.tempDir, `${archiveName}.${this.config.format}`);

    // 计算总文件数
    let totalFiles = 0;
    for (const sourcePath of sourcePaths) {
      try {
        const stats = await fs.stat(sourcePath);
        if (stats.isFile()) {
          totalFiles++;
        } else if (stats.isDirectory()) {
          const dirStats = await this.calculateDirectoryStats(sourcePath);
          totalFiles += dirStats.fileCount;
        }
      } catch (error) {
        archiveLogger.warn(`无法统计文件: ${sourcePath}`, error);
      }
    }

    const task: ArchiveTask = {
      id: taskId,
      name: archiveName,
      sourcePaths,
      outputPath,
      status: "pending",
      progress: 0,
      totalFiles,
      processedFiles: 0,
      startTime: new Date(),
      metadata: options?.metadata,
    };

    this.tasks.set(taskId, task);
    this.emit("task-created", taskId, task);

    return taskId;
  }

  /**
   * 开始压缩任务
   */
  async startArchiveTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`压缩任务不存在: ${taskId}`);
    }

    if (this.activeTasks.size >= this.config.maxConcurrent) {
      throw new Error("已达到最大并发压缩任务数");
    }

    this.activeTasks.add(taskId);
    this.updateTaskStatus(taskId, "compressing");

    try {
      const result = await this.performCompression(task);
      this.updateTaskStatus(taskId, "completed");
      this.emit("task-completed", taskId, result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.updateTaskStatus(taskId, "error", errorMessage);
      this.emit("task-error", taskId, errorMessage);
      throw error;
    } finally {
      this.activeTasks.delete(taskId);
    }
  }

  /**
   * 取消压缩任务
   */
  async cancelArchiveTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`压缩任务不存在: ${taskId}`);
    }

    this.updateTaskStatus(taskId, "cancelled");
    this.activeTasks.delete(taskId);
    this.emit("task-cancelled", taskId);

    // 清理临时文件
    try {
      await fs.unlink(task.outputPath);
    } catch (error) {
      archiveLogger.warn(`清理临时文件失败: ${task.outputPath}`, error);
    }
  }

  /**
   * 获取任务信息
   */
  getTask(taskId: string): ArchiveTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): ArchiveTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 清理已完成的任务
   */
  async cleanupCompletedTasks(): Promise<void> {
    const completedTasks = Array.from(this.tasks.values()).filter((task) => ["completed", "error", "cancelled"].includes(task.status));

    for (const task of completedTasks) {
      try {
        // 清理临时文件
        await fs.unlink(task.outputPath);
      } catch (error) {
        archiveLogger.warn(`清理临时文件失败: ${task.outputPath}`, error);
      }
      this.tasks.delete(task.id);
    }
  }

  // ========== 私有方法 ==========

  private generateTaskId(): string {
    return `archive_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async calculateDirectoryStats(dirPath: string): Promise<{ size: number; fileCount: number }> {
    let size = 0;
    let fileCount = 0;

    try {
      // 首先检查路径是否确实是目录
      const pathStats = await fs.stat(dirPath);
      if (!pathStats.isDirectory()) {
        archiveLogger.warn(`路径不是目录，跳过统计: ${dirPath}`);
        return { size: 0, fileCount: 0 };
      }

      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          size += stats.size;
          fileCount++;
        } else if (entry.isDirectory()) {
          const subStats = await this.calculateDirectoryStats(fullPath);
          size += subStats.size;
          fileCount += subStats.fileCount;
        }
      }
    } catch (error) {
      archiveLogger.warn(`计算目录统计信息失败: ${dirPath}`, error);
    }

    return { size, fileCount };
  }

  /**
   * 计算源路径的公共父目录和相对路径
   */
  private calculateRelativePaths(sourcePaths: string[]): { workingDir: string; relativePaths: string[] } {
    if (sourcePaths.length === 0) {
      throw new Error("源路径列表为空");
    }

    if (sourcePaths.length === 1) {
      // 单个路径的情况
      const singlePath = sourcePaths[0];
      const parentDir = path.dirname(singlePath);
      const relativePath = path.basename(singlePath);
      archiveLogger.info(`📦 单个路径处理: 工作目录=${parentDir}, 相对路径=${relativePath}`);
      return {
        workingDir: parentDir,
        relativePaths: [relativePath],
      };
    }

    // 多个路径的情况，找到真正的公共父目录
    const normalizedPaths = sourcePaths.map((p) => path.resolve(p));

    // 改进的公共父目录查找算法
    let commonParent = this.findTrueCommonParent(normalizedPaths);

    // 计算相对路径
    const relativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));

    archiveLogger.info(`📦 标准化路径:`, normalizedPaths);
    archiveLogger.info(`📦 公共父目录: ${commonParent}`);
    archiveLogger.info(`📦 计算的相对路径:`, relativePaths);

    // 验证相对路径的正确性
    const hasEmptyRelativePath = relativePaths.some((rp) => rp === "" || rp === ".");
    if (hasEmptyRelativePath) {
      // 如果有空的相对路径，说明公共父目录设置过深，需要向上调整
      archiveLogger.info(`📦 检测到空相对路径，调整公共父目录`);
      commonParent = path.dirname(commonParent);
      const adjustedRelativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));

      archiveLogger.info(`📦 调整后的公共父目录: ${commonParent}`);
      archiveLogger.info(`📦 调整后的相对路径:`, adjustedRelativePaths);

      return {
        workingDir: commonParent,
        relativePaths: adjustedRelativePaths,
      };
    }

    return {
      workingDir: commonParent,
      relativePaths,
    };
  }

  /**
   * 找到真正的公共父目录
   */
  private findTrueCommonParent(normalizedPaths: string[]): string {
    if (normalizedPaths.length === 0) {
      throw new Error("路径列表为空");
    }

    if (normalizedPaths.length === 1) {
      return path.dirname(normalizedPaths[0]);
    }

    // 将所有路径分割成组件
    const pathComponents = normalizedPaths.map((p) => p.split(path.sep).filter(Boolean));

    // 找到最短路径的长度
    const minLength = Math.min(...pathComponents.map((components) => components.length));

    // 找到公共前缀
    let commonLength = 0;
    for (let i = 0; i < minLength; i++) {
      const component = pathComponents[0][i];
      if (pathComponents.every((components) => components[i] === component)) {
        commonLength = i + 1;
      } else {
        break;
      }
    }

    if (commonLength === 0) {
      // 没有公共前缀，使用根目录
      // Windows下返回第一个路径的根目录（如 C:\），Unix下返回 /
      if (process.platform === "win32" && normalizedPaths.length > 0) {
        const firstPath = normalizedPaths[0];
        const parsed = path.parse(firstPath);
        return parsed.root; // 例如 "C:\"
      }
      return path.sep;
    }

    // 构建公共父目录路径
    const commonComponents = pathComponents[0].slice(0, commonLength);
    let commonParent: string;

    // Windows系统下的特殊处理
    if (process.platform === "win32" && commonComponents.length > 0) {
      // 在Windows下，直接使用path.join构建路径，不添加额外的分隔符
      commonParent = path.join(...commonComponents);
    } else {
      // Unix/Linux/Mac系统，需要添加根路径分隔符
      commonParent = path.sep + path.join(...commonComponents);
    }

    archiveLogger.info(`📦 公共前缀长度: ${commonLength}, 公共父目录: ${commonParent}`);

    return commonParent;
  }

  private async performCompression(task: ArchiveTask): Promise<ArchiveResult> {
    const startTime = Date.now();

    try {
      // 确保输出目录存在
      await fs.mkdir(path.dirname(task.outputPath), { recursive: true });

      // 删除已存在的压缩包，避免7z更新模式导致重复内容
      try {
        await fs.unlink(task.outputPath);
        archiveLogger.info(`📦 删除已存在的压缩包: ${task.outputPath}`);
      } catch (error) {
        // 文件不存在是正常的，忽略错误
      }

      // 计算公共父目录和相对路径
      const { workingDir, relativePaths } = this.calculateRelativePaths(task.sourcePaths);

      archiveLogger.info(`📦 工作目录: ${workingDir}`);

      // 构建7z命令参数
      const args = [
        "a", // 添加到压缩包
        "-t7z", // 7z格式
        `-mx${this.config.compressionLevel}`, // 压缩级别
        "-y", // 自动回答yes
        "-bb1", // 设置输出日志级别
        "-ms=off", // 关闭固实压缩，避免更新模式问题
        "-mmt=on", // 启用多线程
        task.outputPath,
        ...relativePaths,
      ];

      // 🔍 验证路径存在性
      archiveLogger.info(`🔍 验证源文件/目录存在性:`);
      const invalidPaths: string[] = [];

      for (let i = 0; i < relativePaths.length; i++) {
        const relativePath = relativePaths[i];
        const fullPath = path.join(workingDir, relativePath);
        try {
          await fs.stat(fullPath);
        } catch (error) {
          invalidPaths.push(`${relativePath} (${fullPath})`);
        }
      }

      // 如果有无效路径，抛出错误
      if (invalidPaths.length > 0) {
        const errorMsg = `以下路径无效或无法访问: ${invalidPaths.join(", ")}`;
        archiveLogger.error(`❌ 路径验证失败: ${errorMsg}`);
        throw new Error(errorMsg);
      }

      // 执行7z压缩，在工作目录下执行
      await this.execute7zCommand(args, task, workingDir);

      // 获取压缩结果统计
      const stats = await this.getCompressionStats(task);
      const duration = Date.now() - startTime;

      return {
        success: true,
        archivePath: task.outputPath,
        stats: {
          ...stats,
          duration,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  private async execute7zCommand(args: string[], task: ArchiveTask, workingDir?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查工作目录是否存在
      if (workingDir) {
        try {
          require("fs").accessSync(workingDir, require("fs").constants.F_OK);
          archiveLogger.info(`📦 工作目录验证通过: ${workingDir}`);
        } catch (error) {
          const errorMsg = `工作目录不存在或无法访问: ${workingDir}`;
          archiveLogger.error(`❌ ${errorMsg}`, error);
          reject(new Error(errorMsg));
          return;
        }
      }

      // 获取7z可执行文件路径
      let sevenZipPath: string;
      try {
        sevenZipPath = resolve7zipPath();
      } catch (error) {
        const errorMsg = `7zip-bin不可用: ${error instanceof Error ? error.message : String(error)}`;
        archiveLogger.error(`❌ ${errorMsg}`);
        reject(new Error(errorMsg));
        return;
      }

      try {
        require("fs").accessSync(sevenZipPath, require("fs").constants.F_OK);
        archiveLogger.info(`📦 7z可执行文件验证通过: ${sevenZipPath}`);
      } catch (error) {
        const errorMsg = `7z可执行文件不存在或无法访问: ${sevenZipPath}`;
        archiveLogger.error(`❌ ${errorMsg}`, error);
        reject(new Error(errorMsg));
        return;
      }

      archiveLogger.info(`📦 工作目录: ${workingDir || "当前目录"}`);

      const process = spawn(sevenZipPath, args, {
        cwd: workingDir, // 设置工作目录
      });
      let stderr = "";
      let stdout = "";

      // 添加spawn错误处理
      process.on("error", (error) => {
        const errorMsg = `spawn进程启动失败: ${error.message}`;
        archiveLogger.error(`❌ ${errorMsg}`, error);
        reject(new Error(errorMsg));
      });

      process.stderr.on("data", (data) => {
        const errorText = data.toString();
        stderr += errorText;
        archiveLogger.warn(`📦 7z stderr: ${errorText.trim()}`);
      });

      process.stdout.on("data", (data) => {
        // 解析7z输出来更新进度
        const output = data.toString();
        stdout += output;
        this.parseProgressFromOutput(output, task);
      });

      process.on("close", (code) => {
        // 分析7z输出，查找可能的问题
        if (code === 0) {
          archiveLogger.info(`✅ 7z压缩成功完成`);
          resolve();
        } else {
          const errorMsg = `7z压缩失败，退出码: ${code}, 错误: ${stderr}`;
          archiveLogger.error(`❌ ${errorMsg}`);
          reject(new Error(errorMsg));
        }
      });
    });
  }

  private parseProgressFromOutput(output: string, task: ArchiveTask): void {
    // 简单的进度解析，可以根据7z的实际输出格式进行优化
    const lines = output.split("\n");
    for (const line of lines) {
      if (line.includes("Compressing")) {
        const match = line.match(/Compressing\s+(.+)/);
        if (match) {
          task.currentFile = match[1];
          task.processedFiles++;
          task.progress = Math.min(95, Math.round((task.processedFiles / task.totalFiles) * 100));
          this.emit("task-progress", task.id, task.progress, task.currentFile);
        }
      }
    }
  }

  private async getCompressionStats(task: ArchiveTask): Promise<{
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    fileCount: number;
  }> {
    try {
      // 计算原始大小
      let originalSize = 0;
      let fileCount = 0;

      for (const sourcePath of task.sourcePaths) {
        try {
          const pathStats = await fs.stat(sourcePath);
          if (pathStats.isFile()) {
            originalSize += pathStats.size;
            fileCount++;
          } else if (pathStats.isDirectory()) {
            const stats = await this.calculateDirectoryStats(sourcePath);
            originalSize += stats.size;
            fileCount += stats.fileCount;
          }
        } catch (error) {
          archiveLogger.warn(`无法获取源路径统计信息: ${sourcePath}`, error);
        }
      }

      // 获取压缩后大小
      const compressedStats = await fs.stat(task.outputPath);
      const compressedSize = compressedStats.size;
      const compressionRatio = originalSize > 0 ? (1 - compressedSize / originalSize) * 100 : 0;

      return {
        originalSize,
        compressedSize,
        compressionRatio,
        fileCount,
      };
    } catch (error) {
      archiveLogger.warn("获取压缩统计信息失败:", error);
      return {
        originalSize: 0,
        compressedSize: 0,
        compressionRatio: 0,
        fileCount: 0,
      };
    }
  }

  private updateTaskStatus(taskId: string, status: ArchiveStatus, error?: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = status;
    if (error) {
      task.error = error;
    }
    if (status === "completed" || status === "error" || status === "cancelled") {
      task.endTime = new Date();
    }
  }
}
